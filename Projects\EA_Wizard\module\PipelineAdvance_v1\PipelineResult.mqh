#property strict

#include "TradingEvent.mqh"

//+------------------------------------------------------------------+
//| PipelineResult.mqh                                               |
//| 流水線結果類 - 用於存儲流水線執行結果                            |
//| 擴展版本，支持錯誤級別和時間戳                                   |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| 流水線結果類                                                     |
//| 用於存儲和管理流水線執行的結果信息                               |
//| 包含成功狀態、消息、來源、錯誤級別和時間戳                       |
//+------------------------------------------------------------------+
class PipelineResult
{
private:
    bool m_success;                     // 執行是否成功
    string m_message;                   // 結果消息
    string m_source;                    // 結果來源
    datetime m_timestamp;               // 時間戳
    ENUM_ERROR_LEVEL m_errorLevel;      // 錯誤級別

public:
    //+------------------------------------------------------------------+
    //| 構造函數                                                         |
    //| @param success 執行是否成功                                      |
    //| @param message 結果消息                                          |
    //| @param source 結果來源                                           |
    //| @param errorLevel 錯誤級別，默認為 ERROR_LEVEL_INFO              |
    //+------------------------------------------------------------------+
    PipelineResult(bool success, 
                   string message, 
                   string source, 
                   ENUM_ERROR_LEVEL errorLevel = ERROR_LEVEL_INFO)
        : m_success(success),
          m_message(message),
          m_source(source),
          m_timestamp(TimeCurrent()),
          m_errorLevel(errorLevel)
    {
    }

    //+------------------------------------------------------------------+
    //| 析構函數                                                         |
    //+------------------------------------------------------------------+
    virtual ~PipelineResult()
    {
    }

    //+------------------------------------------------------------------+
    //| 檢查是否成功                                                     |
    //| @return bool true表示成功，false表示失敗                         |
    //+------------------------------------------------------------------+
    bool IsSuccess() const
    {
        return m_success;
    }

    //+------------------------------------------------------------------+
    //| 獲取結果消息                                                     |
    //| @return string 結果消息                                          |
    //+------------------------------------------------------------------+
    string GetMessage() const
    {
        return m_message;
    }

    //+------------------------------------------------------------------+
    //| 獲取結果來源                                                     |
    //| @return string 結果來源                                          |
    //+------------------------------------------------------------------+
    string GetSource() const
    {
        return m_source;
    }

    //+------------------------------------------------------------------+
    //| 獲取時間戳                                                       |
    //| @return datetime 結果創建時間                                    |
    //+------------------------------------------------------------------+
    datetime GetTimestamp() const
    {
        return m_timestamp;
    }

    //+------------------------------------------------------------------+
    //| 獲取錯誤級別                                                     |
    //| @return ENUM_ERROR_LEVEL 錯誤級別                                |
    //+------------------------------------------------------------------+
    ENUM_ERROR_LEVEL GetErrorLevel() const
    {
        return m_errorLevel;
    }

    //+------------------------------------------------------------------+
    //| 轉換為字符串表示                                                 |
    //| @return string 格式化的結果字符串                                |
    //+------------------------------------------------------------------+
    string ToString() const
    {
        string statusStr = m_success ? "成功" : "失敗";
        string levelStr = ErrorLevelToString(m_errorLevel);
        string timeStr = TimeToString(m_timestamp, TIME_DATE | TIME_MINUTES);
        
        return StringFormat("[%s] %s - %s (%s) [%s]",
                          timeStr,
                          m_source,
                          m_message,
                          statusStr,
                          levelStr);
    }

    //+------------------------------------------------------------------+
    //| 錯誤級別轉字符串                                                 |
    //| @param level 錯誤級別                                            |
    //| @return string 錯誤級別字符串                                    |
    //+------------------------------------------------------------------+
    static string ErrorLevelToString(ENUM_ERROR_LEVEL level)
    {
        switch(level)
        {
            case ERROR_LEVEL_INFO:     return "信息";
            case ERROR_LEVEL_WARNING:  return "警告";
            case ERROR_LEVEL_ERROR:    return "錯誤";
            case ERROR_LEVEL_CRITICAL: return "嚴重";
            default:                   return "未知";
        }
    }

    //+------------------------------------------------------------------+
    //| 設置成功狀態                                                     |
    //| @param success 新的成功狀態                                      |
    //+------------------------------------------------------------------+
    void SetSuccess(bool success)
    {
        m_success = success;
        m_timestamp = TimeCurrent(); // 更新時間戳
    }

    //+------------------------------------------------------------------+
    //| 設置消息                                                         |
    //| @param message 新的消息                                          |
    //+------------------------------------------------------------------+
    void SetMessage(string message)
    {
        m_message = message;
        m_timestamp = TimeCurrent(); // 更新時間戳
    }

    //+------------------------------------------------------------------+
    //| 設置錯誤級別                                                     |
    //| @param errorLevel 新的錯誤級別                                   |
    //+------------------------------------------------------------------+
    void SetErrorLevel(ENUM_ERROR_LEVEL errorLevel)
    {
        m_errorLevel = errorLevel;
        m_timestamp = TimeCurrent(); // 更新時間戳
    }

    //+------------------------------------------------------------------+
    //| 更新結果                                                         |
    //| @param success 執行是否成功                                      |
    //| @param message 結果消息                                          |
    //| @param errorLevel 錯誤級別                                       |
    //+------------------------------------------------------------------+
    void Update(bool success, string message, ENUM_ERROR_LEVEL errorLevel = ERROR_LEVEL_INFO)
    {
        m_success = success;
        m_message = message;
        m_errorLevel = errorLevel;
        m_timestamp = TimeCurrent();
    }

    //+------------------------------------------------------------------+
    //| 複製結果                                                         |
    //| @return PipelineResult* 新的結果副本                             |
    //+------------------------------------------------------------------+
    PipelineResult* Copy() const
    {
        return new PipelineResult(m_success, m_message, m_source, m_errorLevel);
    }

    //+------------------------------------------------------------------+
    //| 比較兩個結果是否相等                                             |
    //| @param other 另一個結果對象                                      |
    //| @return bool true表示相等，false表示不相等                       |
    //+------------------------------------------------------------------+
    bool Equals(const PipelineResult* other) const
    {
        if(other == NULL) return false;
        
        return m_success == other.IsSuccess() &&
               m_message == other.GetMessage() &&
               m_source == other.GetSource() &&
               m_errorLevel == other.GetErrorLevel();
    }

    //+------------------------------------------------------------------+
    //| 檢查是否為錯誤結果                                               |
    //| @return bool true表示是錯誤結果，false表示不是                   |
    //+------------------------------------------------------------------+
    bool IsError() const
    {
        return !m_success || m_errorLevel == ERROR_LEVEL_ERROR || m_errorLevel == ERROR_LEVEL_CRITICAL;
    }

    //+------------------------------------------------------------------+
    //| 檢查是否為警告結果                                               |
    //| @return bool true表示是警告結果，false表示不是                   |
    //+------------------------------------------------------------------+
    bool IsWarning() const
    {
        return m_errorLevel == ERROR_LEVEL_WARNING;
    }

    //+------------------------------------------------------------------+
    //| 檢查是否為信息結果                                               |
    //| @return bool true表示是信息結果，false表示不是                   |
    //+------------------------------------------------------------------+
    bool IsInfo() const
    {
        return m_errorLevel == ERROR_LEVEL_INFO;
    }
};
