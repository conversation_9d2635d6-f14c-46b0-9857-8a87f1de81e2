#property strict

#include "../TradingController.mqh"

//+------------------------------------------------------------------+
//| TestTradingController.mq4                                        |
//| TradingController 模組測試腳本                                   |
//| 測試 TradingController 的基本功能和生命週期方法                  |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| 腳本程序開始函數                                                 |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("=== TradingController 測試開始 ===");
    
    // 測試 TradingController 創建
    TestTradingControllerCreation();
    
    // 測試生命週期方法
    TestLifecycleMethods();
    
    // 測試 getter 方法
    TestGetterMethods();
    
    Print("=== TradingController 測試完成 ===");
}

//+------------------------------------------------------------------+
//| 測試 TradingController 創建                                      |
//+------------------------------------------------------------------+
void TestTradingControllerCreation()
{
    Print("--- 測試 TradingController 創建 ---");
    
    // 測試默認構造函數
    TradingController* controller1 = new TradingController();
    if(controller1 != NULL)
    {
        Print("✓ 默認構造函數測試通過");
        Print("  控制器名稱: " + controller1.GetName());
        Print("  控制器類型: " + controller1.GetType());
    }
    else
    {
        Print("✗ 默認構造函數測試失敗");
    }
    
    // 測試自定義名稱構造函數
    TradingController* controller2 = new TradingController("TestController");
    if(controller2 != NULL)
    {
        Print("✓ 自定義名稱構造函數測試通過");
        Print("  控制器名稱: " + controller2.GetName());
        Print("  控制器類型: " + controller2.GetType());
    }
    else
    {
        Print("✗ 自定義名稱構造函數測試失敗");
    }
    
    // 測試驅動器獲取
    TradingPipelineDriver* driver = controller1.GetDriver();
    if(driver != NULL)
    {
        Print("✓ 驅動器獲取測試通過");
        Print("  驅動器名稱: " + driver.GetName());
        Print("  驅動器類型: " + driver.GetType());
        Print("  驅動器初始化狀態: " + (driver.IsInitialized() ? "已初始化" : "未初始化"));
    }
    else
    {
        Print("✗ 驅動器獲取測試失敗");
    }
    
    // 清理
    delete controller1;
    delete controller2;
}

//+------------------------------------------------------------------+
//| 測試生命週期方法                                                 |
//+------------------------------------------------------------------+
void TestLifecycleMethods()
{
    Print("--- 測試生命週期方法 ---");
    
    TradingController* controller = new TradingController("LifecycleTest");
    
    // 測試 OnInit
    Print("測試 OnInit 方法...");
    ENUM_INIT_RETCODE initResult = controller.OnInit();
    Print("OnInit 結果: " + EnumToString(initResult));
    Print("初始化狀態: " + (controller.IsInitialized() ? "已初始化" : "未初始化"));
    
    // 獲取初始化結果
    PipelineResult* initPipelineResult = controller.GetResult();
    if(initPipelineResult != NULL)
    {
        Print("初始化結果詳情: " + initPipelineResult.ToString());
    }
    
    // 測試 OnTick（只有在初始化成功後才測試）
    if(controller.IsInitialized())
    {
        Print("測試 OnTick 方法...");
        controller.OnTick();
        
        PipelineResult* tickResult = controller.GetResult();
        if(tickResult != NULL)
        {
            Print("OnTick 結果詳情: " + tickResult.ToString());
        }
    }
    else
    {
        Print("跳過 OnTick 測試（控制器未初始化）");
    }
    
    // 測試 OnDeinit
    Print("測試 OnDeinit 方法...");
    controller.OnDeinit(REASON_PROGRAM);
    
    PipelineResult* deinitResult = controller.GetResult();
    if(deinitResult != NULL)
    {
        Print("OnDeinit 結果詳情: " + deinitResult.ToString());
    }
    
    Print("清理後初始化狀態: " + (controller.IsInitialized() ? "已初始化" : "未初始化"));
    
    // 清理
    delete controller;
}

//+------------------------------------------------------------------+
//| 測試 getter 方法                                                |
//+------------------------------------------------------------------+
void TestGetterMethods()
{
    Print("--- 測試 Getter 方法 ---");
    
    TradingController* controller = new TradingController("GetterTest");
    
    // 測試基本 getter 方法
    Print("控制器名稱: " + controller.GetName());
    Print("控制器類型: " + controller.GetType());
    Print("初始化狀態: " + (controller.IsInitialized() ? "已初始化" : "未初始化"));
    
    // 測試結果獲取
    PipelineResult* result = controller.GetResult();
    if(result != NULL)
    {
        Print("當前結果: " + result.ToString());
        Print("結果成功狀態: " + (result.IsSuccess() ? "成功" : "失敗"));
        Print("結果消息: " + result.GetMessage());
        Print("結果來源: " + result.GetSource());
        Print("錯誤級別: " + PipelineResult::ErrorLevelToString(result.GetErrorLevel()));
    }
    else
    {
        Print("✗ 結果獲取失敗 - 結果為 NULL");
    }
    
    // 測試驅動器獲取
    TradingPipelineDriver* driver = controller.GetDriver();
    if(driver != NULL)
    {
        Print("驅動器信息:");
        Print("  名稱: " + driver.GetName());
        Print("  類型: " + driver.GetType());
        Print("  初始化狀態: " + (driver.IsInitialized() ? "已初始化" : "未初始化"));
        
        // 測試驅動器的組件
        TradingPipelineContainerManager* manager = driver.GetManager();
        TradingPipelineRegistry* registry = driver.GetRegistry();
        TradingPipelineExplorer* explorer = driver.GetExplorer();
        
        Print("  管理器: " + (manager != NULL ? "可用" : "不可用"));
        Print("  註冊器: " + (registry != NULL ? "可用" : "不可用"));
        Print("  探索器: " + (explorer != NULL ? "可用" : "不可用"));
    }
    else
    {
        Print("✗ 驅動器獲取失敗");
    }
    
    // 清理
    delete controller;
}

//+------------------------------------------------------------------+
//| 腳本程序結束函數                                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("測試腳本結束，原因: " + (string)reason);
}
